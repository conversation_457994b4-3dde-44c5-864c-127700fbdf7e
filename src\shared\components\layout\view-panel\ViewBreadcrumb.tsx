import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Breadcrumb, Icon } from '@/shared/components/common';
import type { BreadcrumbItem } from '@/shared/components/common/Breadcrumb/Breadcrumb';

interface ViewBreadcrumbProps {
  title: string;
  className?: string;
}

const ViewBreadcrumb: React.FC<ViewBreadcrumbProps> = ({ title, className = '' }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation([
    'common',
    'marketing',
    'admin',
    'marketplace',
    'components',
    'data',
    'business',
    'integration',
    'tools',
    'user-dataset',

  ]);

  // Tạo breadcrumb items dựa trên đường dẫn hiện tại
  const generateBreadcrumbItems = (): BreadcrumbItem[] => {
    // Luôn có item Home (hiển thị icon và text "Trang chủ")
    const items: BreadcrumbItem[] = [
      {
        label: t('common:home', 'Trang chủ'),
        path: '/',
        icon: <Icon name="home" size="sm" />,
        onClick: () => navigate('/'),
      },
    ];

    // Nếu không phải trang chủ, thêm trang hiện tại
    if (location.pathname !== '/') {
      // Xử lý các trường hợp đặc biệt
      if (location.pathname.startsWith('/components')) {
        // Xử lý các trang components
        if (location.pathname === '/components') {
          // Trang components chính
          items.push({
            label: t('components:library.title', 'Thư viện Components'),
          });
        } else {
          // Các trang con của components
          items.push({
            label: t('components:library.title', 'Thư viện Components'),
            path: '/components',
            onClick: () => navigate('/components'),
          });

          // Thêm trang hiện tại
          {
            // Kiểm tra xem title có phải là key translation không
            const isTranslationKey = title.includes(':') && !title.includes(' ');
            items.push({
              label: isTranslationKey ? t(title) : title,
            });
          }
        }
      } else {
        // Xử lý các trang khác
        switch (location.pathname) {
          case '/animation-demo':
            items.push({
              label: t('common:animation', 'Hiệu ứng'),
            });
            break;
          case '/responsive-demo':
            items.push({
              label: t('common:componentsText', 'Components'),
            });
            break;
          case '/ai-agents':
            items.push({
              label: t('chat:aiAgents', 'AI Agents'),
            });
            break;

          // Marketplace module
          case '/marketplace':
            items.push({
              label: t('marketplace:title', 'Marketplace'),
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            break;
          case '/marketplace/cart':
            items.push({
              label: t('marketplace:title', 'Marketplace'),
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            items.push({
              label: t('marketplace:cart.title', 'Giỏ hàng'),
            });
            break;
          case location.pathname.startsWith('/marketplace/product/') ? location.pathname : '':
            items.push({
              label: t('marketplace:title', 'Marketplace'),
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            items.push({
              label: t('marketplace:product.detail.information', 'Thông tin sản phẩm'),
            });
            break;
          case location.pathname.startsWith('/marketplace/category/') ? location.pathname : '':
            items.push({
              label: t('marketplace:title', 'Marketplace'),
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            items.push({
              label: t('marketplace:categories', 'Danh mục'),
            });
            break;
          case location.pathname.startsWith('/business/products/edit/') ? location.pathname : '':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:product.title', 'Sản phẩm'),
              path: '/business/product',
              onClick: () => navigate('/business/product'),
            });
            items.push({
              label: t('business:product.form.editTitle', 'Chỉnh sửa sản phẩm'),
            });
            break;

          // Marketing module
          case '/marketing':
            items.push({
              label: t('marketing:title', 'Marketing'),
            });
            break;
          case '/marketing/dashboard':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:dashboard.title', 'Marketing Dashboard'),
            });
            break;
          case '/marketing/audience':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:menu.audience', 'Đối tượng'),
            });
            break;

          // Business module
          case '/business':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
            });
            break;
          case '/business/product':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:product.title', 'Sản phẩm'),
            });
            break;
          case '/business/customer':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:customer.title', 'Khách hàng'),
            });
            break;
          case '/business/order':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:order.title', 'Đơn hàng'),
            });
            break;
          case location.pathname.startsWith('/business/order/') ? location.pathname : '':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:order.title', 'Đơn hàng'),
              path: '/business/order',
              onClick: () => navigate('/business/order'),
            });
            items.push({
              label: t('business:order.detail', 'Chi tiết đơn hàng'),
            });
            break;
          case '/business/conversion':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:conversion.title', 'Chuyển đổi'),
            });
            break;
          case '/business/report':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:report.title', 'Báo cáo'),
            });
            break;
          case '/business/inventory':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:inventory.title', 'Quản lý kho'),
            });
            break;
          case '/business/shop':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:shop.title', 'Quản lý cửa hàng'),
            });
            break;

          case '/business/products/create':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:product.title', 'Sản phẩm'),
              path: '/business/product',
              onClick: () => navigate('/business/product'),
            });
            items.push({
              label: t('business:product.form.createTitle', 'Tạo sản phẩm'),
            });
            break;
          case location.pathname.startsWith('/business/conversion/') ? location.pathname : '':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:conversion.title', 'Chuyển đổi'),
              path: '/business/conversion',
              onClick: () => navigate('/business/conversion'),
            });
            items.push({
              label: t('business:conversion.detail.title', 'Chi tiết chuyển đổi'),
            });
            break;
          case location.pathname.startsWith('/business/inventory/') ? location.pathname : '':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:inventory.title', 'Quản lý kho'),
              path: '/business/inventory',
              onClick: () => navigate('/business/inventory'),
            });
            items.push({
              label: t('business:warehouse.detailTitle', 'Chi tiết kho'),
            });
            break;
          case '/business/virtual-warehouse':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:virtualWarehouse.title', 'Quản lý kho ảo'),
            });
            break;
          case location.pathname.startsWith('/business/warehouse/virtual/')
            ? location.pathname
            : '':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:virtualWarehouse.title', 'Quản lý kho ảo'),
              path: '/business/virtual-warehouse',
              onClick: () => navigate('/business/virtual-warehouse'),
            });
            items.push({
              label: t('business:warehouse.virtual.detailTitle', 'Chi tiết kho ảo'),
            });
            break;

          // Data module
          case '/data':
            items.push({
              label: t('data:title', 'Quản lý dữ liệu'),
            });
            break;
          case '/data/url':
            items.push({
              label: t('data:title', 'Quản lý dữ liệu'),
              path: '/data',
              onClick: () => navigate('/data'),
            });
            items.push({
              label: t('data:url.title', 'Quản lý URL'),
            });
            break;
          case '/data/media':
            items.push({
              label: t('data:title', 'Quản lý dữ liệu'),
              path: '/data',
              onClick: () => navigate('/data'),
            });
            items.push({
              label: t('data:media.title', 'Quản lý Media'),
            });
            break;
          case '/data/knowledge-files':
            items.push({
              label: t('data:title', 'Quản lý dữ liệu'),
              path: '/data',
              onClick: () => navigate('/data'),
            });
            items.push({
              label: t('data:knowledgeFiles.title', 'Knowledge Files'),
            });
            break;
          case '/data/vector-store':
            items.push({
              label: t('data:title', 'Quản lý dữ liệu'),
              path: '/data',
              onClick: () => navigate('/data'),
            });
            items.push({
              label: t('data:vectorStore.title', 'Vector Store'),
            });
            break;

          // Admin Data module
          case '/admin/data':
            items.push({
              label: t('admin:data.title', 'Quản lý dữ liệu Admin'),
            });
            break;
          case '/admin/data/media':
            items.push({
              label: t('admin:data.title', 'Quản lý dữ liệu Admin'),
              path: '/admin/data',
              onClick: () => navigate('/admin/data'),
            });
            items.push({
              label: t('admin:data.media.title', 'Quản lý Media'),
            });
            break;
          case '/admin/data/url':
            items.push({
              label: t('admin:data.title', 'Quản lý dữ liệu Admin'),
              path: '/admin/data',
              onClick: () => navigate('/admin/data'),
            });
            items.push({
              label: t('admin:data.url.title', 'Quản lý URL'),
            });
            break;
          case '/admin/data/knowledge-files':
            items.push({
              label: t('admin:data.title', 'Quản lý dữ liệu Admin'),
              path: '/admin/data',
              onClick: () => navigate('/admin/data'),
            });
            items.push({
              label: t('admin:data.knowledgeFiles.title', 'Quản lý Knowledge Files'),
            });
            break;
          case '/admin/data/vector-store':
            items.push({
              label: t('admin:data.title', 'Quản lý dữ liệu Admin'),
              path: '/admin/data',
              onClick: () => navigate('/admin/data'),
            });
            items.push({
              label: t('admin:data.vectorStore.title', 'Quản lý Vector Store'),
            });
            break;
          case '/marketing/segment':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:menu.segment', 'Phân đoạn'),
            });
            break;
          case '/marketing/campaign':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:menu.campaign', 'Chiến dịch'),
            });
            break;
          case '/marketing/tags':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:menu.tags', 'Thẻ'),
            });
            break;
          case '/marketing/custom-fields':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:menu.customFields', 'Trường tùy chỉnh'),
            });
            break;
          case '/marketing/reports':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:menu.reports', 'Báo cáo'),
            });
            break;
          case '/marketing/template-emails':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:templateEmail.title', 'Quản lý mẫu email'),
            });
            break;
          case '/marketing/sms':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:smsMarketing.title', 'SMS Marketing'),
            });
            break;
          case '/marketing/google-ads':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:googleAds.title', 'Google Ads'),
            });
            break;
          case '/marketing/google-ads/detail':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:googleAds.title', 'Google Ads'),
              path: '/marketing/google-ads',
              onClick: () => navigate('/marketing/google-ads'),
            });
            items.push({
              label: t('marketing:googleAds.detail.title', 'Chi tiết Google Ads'),
            });
            break;
          case '/marketing/google-ads/accounts':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:googleAds.title', 'Google Ads'),
              path: '/marketing/google-ads',
              onClick: () => navigate('/marketing/google-ads'),
            });
            items.push({
              label: t('marketing:googleAds.accounts.title', 'Tài khoản Google Ads'),
            });
            break;
          case '/marketing/google-ads/campaigns':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:googleAds.title', 'Google Ads'),
              path: '/marketing/google-ads',
              onClick: () => navigate('/marketing/google-ads'),
            });
            items.push({
              label: t('marketing:googleAds.campaigns.title', 'Chiến dịch Google Ads'),
            });
            break;
          case '/marketing/facebook-ads':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:facebookAds.title', 'Facebook Ads'),
            });
            break;
          case '/marketing/zalo':
          case '/marketing/zalo/overview':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
            });
            break;
          case '/marketing/zalo/accounts':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
              path: '/marketing/zalo',
              onClick: () => navigate('/marketing/zalo'),
            });
            items.push({
              label: t('marketing:zalo.accounts.title', 'Quản lý Zalo OA'),
            });
            break;
          case location.pathname.match(/^\/marketing\/zalo\/accounts\/[^/]+\/followers$/)
            ? location.pathname
            : '':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
              path: '/marketing/zalo',
              onClick: () => navigate('/marketing/zalo'),
            });
            items.push({
              label: t('marketing:zalo.accounts.title', 'Quản lý Zalo OA'),
              path: '/marketing/zalo/accounts',
              onClick: () => navigate('/marketing/zalo/accounts'),
            });
            items.push({
              label: t('marketing:zalo.followers.title', 'Quản lý Followers'),
            });
            break;
          case '/marketing/zalo/zns':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zalo.title', 'Zalo OA/ZNS'),
              path: '/marketing/zalo',
              onClick: () => navigate('/marketing/zalo'),
            });
            items.push({
              label: t('marketing:zalo.zns.title', 'ZNS Templates'),
            });
            break;
          case '/marketing/email':
          case '/marketing/email/overview':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:email.title', 'Email Marketing'),
            });
            break;
          case '/marketing/email/templates':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:email.title', 'Email Marketing'),
              path: '/marketing/email',
              onClick: () => navigate('/marketing/email'),
            });
            items.push({
              label: t('marketing:email.templates.title', 'Email Templates'),
            });
            break;
          case '/marketing/email/campaigns':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:email.title', 'Email Marketing'),
              path: '/marketing/email',
              onClick: () => navigate('/marketing/email'),
            });
            items.push({
              label: t('marketing:email.campaigns.title', 'Email Campaigns'),
            });
            break;
          case '/marketing/email/analytics':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:email.title', 'Email Marketing'),
              path: '/marketing/email',
              onClick: () => navigate('/marketing/email'),
            });
            items.push({
              label: t('marketing:email.analytics.title', 'Analytics'),
            });
            break;
          case '/marketing/zalo-ads':
          case '/marketing/zalo-ads/overview':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zaloAds.title', 'Zalo Ads'),
            });
            break;
          case '/marketing/zalo-ads/accounts':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zaloAds.title', 'Zalo Ads'),
              path: '/marketing/zalo-ads',
              onClick: () => navigate('/marketing/zalo-ads'),
            });
            items.push({
              label: t('marketing:zaloAds.accounts.title', 'Quản lý tài khoản'),
            });
            break;
          case '/marketing/zalo-ads/campaigns':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zaloAds.title', 'Zalo Ads'),
              path: '/marketing/zalo-ads',
              onClick: () => navigate('/marketing/zalo-ads'),
            });
            items.push({
              label: t('marketing:zaloAds.campaigns.title', 'Quản lý chiến dịch'),
            });
            break;
          case '/marketing/zalo-ads/reports':
            items.push({
              label: t('marketing:title', 'Marketing'),
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: t('marketing:zaloAds.title', 'Zalo Ads'),
              path: '/marketing/zalo-ads',
              onClick: () => navigate('/marketing/zalo-ads'),
            });
            items.push({
              label: t('marketing:zaloAds.reports.title', 'Báo cáo'),
            });
            break;

          // Integration module
          case '/integrations':
            items.push({
              label: t('integration:title', 'Tích hợp'),
            });
            break;
          case '/integrations/email':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:email.title', 'Quản lý Email'),
            });
            break;
          case '/integrations/facebook':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:facebook.title', 'Quản lý Facebook'),
            });
            break;
          case '/integrations/website':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:website.title', 'Quản lý Website'),
            });
            break;
          case '/integrations/shipping':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:shipping.title', 'Quản lý Vận chuyển'),
            });
            break;
          case '/user/bank-accounts':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:bankAccounts.title', 'Quản lý Tài khoản ngân hàng'),
            });
            break;
          case '/user/provider-model':
            items.push({
              label: t('integration:title', 'Tích hợp'),
              path: '/integrations',
              onClick: () => navigate('/integrations'),
            });
            items.push({
              label: t('integration:providerModel.title', 'Quản lý Provider Model'),
            });
            break;

          // Admin Integration module
          case '/admin/integration':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:integration.title', 'Quản lý Tích hợp'),
            });
            break;
          case '/admin/integration/email':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:integration.title', 'Quản lý Tích hợp'),
              path: '/admin/integration',
              onClick: () => navigate('/admin/integration'),
            });
            items.push({
              label: t('admin:integration.email.title', 'Quản lý Email Server'),
            });
            break;
          case '/admin/integration/provider-model':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:integration.title', 'Quản lý Tích hợp'),
              path: '/admin/integration',
              onClick: () => navigate('/admin/integration'),
            });
            items.push({
              label: t('admin:integration.providerModel.title', 'Quản lý Provider Model'),
            });
            break;
          case '/admin/integration/sms':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:integration.title', 'Quản lý Tích hợp'),
              path: '/admin/integration',
              onClick: () => navigate('/admin/integration'),
            });
            items.push({
              label: t('admin:integration.sms.title', 'Quản lý SMS Server'),
            });
            break;
          case '/admin/integration/payment':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:integration.title', 'Quản lý Tích hợp'),
              path: '/admin/integration',
              onClick: () => navigate('/admin/integration'),
            });
            items.push({
              label: t('admin:integration.payment.title', 'Quản lý Payment Gateway'),
            });
            break;
          case '/admin/integration/social':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:integration.title', 'Quản lý Tích hợp'),
              path: '/admin/integration',
              onClick: () => navigate('/admin/integration'),
            });
            items.push({
              label: t('admin:integration.social.title', 'Quản lý Social Media'),
            });
            break;
          case '/admin/integration/api-keys':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:integration.title', 'Quản lý Tích hợp'),
              path: '/admin/integration',
              onClick: () => navigate('/admin/integration'),
            });
            items.push({
              label: t('admin:integration.apiKeys.title', 'Quản lý API Keys'),
            });
            break;
          case '/admin/integration/webhooks':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:integration.title', 'Quản lý Tích hợp'),
              path: '/admin/integration',
              onClick: () => navigate('/admin/integration'),
            });
            items.push({
              label: t('admin:integration.webhooks.title', 'Quản lý Webhooks'),
            });
            break;

          // Admin Tool module
          case '/admin/tools':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:tool.title', 'Quản lý Tools'),
            });
            break;
          case '/admin/tools/list':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:tool.title', 'Quản lý Tools'),
              path: '/admin/tools',
              onClick: () => navigate('/admin/tools'),
            });
            items.push({
              label: t('admin:tool.list.title', 'Danh sách Tools'),
            });
            break;
          case '/admin/tools/trash':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('admin:tool.title', 'Quản lý Tools'),
              path: '/admin/tools',
              onClick: () => navigate('/admin/tools'),
            });
            items.push({
              label: t('admin:tool.trash.title', 'Danh sách Tools đã xóa'),
            });
            break;

          // User Tools module
          case '/tools':
            items.push({
              label: t('tools:title', 'Tools'),
            });
            break;
          case '/tools/list':
            items.push({
              label: t('tools:title', 'Tools'),
              path: '/tools',
              onClick: () => navigate('/tools'),
            });
            items.push({
              label: t('tools:list.title', 'Danh sách Tools'),
            });
            break;
          case location.pathname.startsWith('/tools/') ? location.pathname : '':
            // Xử lý các trang con của tools (tool detail, tool versions, etc.)
            items.push({
              label: t('tools:title', 'Tools'),
              path: '/tools',
              onClick: () => navigate('/tools'),
            });
            // Thêm trang hiện tại dựa trên title
            {
              const isTranslationKey = title.includes(':') && !title.includes(' ');
              items.push({
                label: isTranslationKey ? t(title) : title,
              });
            }
            break;

          // Admin Business module
          case '/admin/business':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
            });
            break;
          case '/admin/business/product':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/admin/business',
              onClick: () => navigate('/admin/business'),
            });
            items.push({
              label: t('business:product.title', 'Quản lý sản phẩm'),
            });
            break;
          case '/admin/business/conversion':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/admin/business',
              onClick: () => navigate('/admin/business'),
            });
            items.push({
              label: t('business:conversion.title', 'Quản lý chuyển đổi'),
            });
            break;
          case '/admin/business/order':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/admin/business',
              onClick: () => navigate('/admin/business'),
            });
            items.push({
              label: t('business:order.title', 'Quản lý đơn hàng'),
            });
            break;
          case '/admin/business/warehouse':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/admin/business',
              onClick: () => navigate('/admin/business'),
            });
            items.push({
              label: t('business:warehouse.title', 'Quản lý kho'),
            });
            break;
          case '/admin/business/custom-field':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/admin/business',
              onClick: () => navigate('/admin/business'),
            });
            items.push({
              label: t('business:customField.title', 'Trường tùy chỉnh'),
            });
            break;
          case '/admin/business/warehouse-custom-field':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/admin/business',
              onClick: () => navigate('/admin/business'),
            });
            items.push({
              label: t('business:warehouseCustomField.title', 'Trường tùy chỉnh kho'),
            });
            break;
          case '/admin/business/file':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/admin/business',
              onClick: () => navigate('/admin/business'),
            });
            items.push({
              label: t('business:file.title', 'Quản lý file'),
            });
            break;
          case '/admin/business/folder':
            items.push({
              label: t('admin:title', 'Quản trị'),
              path: '/admin',
              onClick: () => navigate('/admin'),
            });
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/admin/business',
              onClick: () => navigate('/admin/business'),
            });
            items.push({
              label: t('business:folder.title', 'Quản lý thư mục'),
            });
            break;

          case '/business/custom-field':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:customField.title', 'Trường tùy chỉnh'),
            });
            break;
          case '/business/custom-group-form':
            items.push({
              label: t('business:title', 'Quản lý kinh doanh'),
              path: '/business',
              onClick: () => navigate('/business'),
            });
            items.push({
              label: t('business:customGroupForm.title', 'Nhóm trường tùy chỉnh'),
            });
            break;

          // User Dataset module
          case '/user-dataset':
            items.push({
              label: t('user-dataset:title', 'Quản lý Dataset & Model'),
            });
            break;
          case '/user-dataset/data-fine-tune':
            items.push({
              label: t('user-dataset:title', 'Quản lý Dataset & Model'),
              path: '/user-dataset',
              onClick: () => navigate('/user-dataset'),
            });
            items.push({
              label: t('user-dataset:dataFineTune.title', 'Dataset Fine-tune'),
            });
            break;
          case '/user-dataset/create-openai':
            items.push({
              label: t('user-dataset:title', 'Quản lý Dataset & Model'),
              path: '/user-dataset',
              onClick: () => navigate('/user-dataset'),
            });
            items.push({
              label: t('user-dataset:createDataset.openai.title', 'Tạo Dataset OpenAI'),
            });
            break;
          case '/user-dataset/create-google':
            items.push({
              label: t('user-dataset:title', 'Quản lý Dataset & Model'),
              path: '/user-dataset',
              onClick: () => navigate('/user-dataset'),
            });
            items.push({
              label: t('user-dataset:createDataset.google.title', 'Tạo Dataset Google'),
            });
            break;

          default: {
            // Nếu không có xử lý đặc biệt, sử dụng title được truyền vào
            // Kiểm tra xem title có phải là key translation không
            const isTranslationKey = title.includes(':') && !title.includes(' ');
            items.push({
              label: isTranslationKey ? t(title) : title,
            });
          }
        }
      }
    }

    return items;
  };

  return (
    <div className="flex items-center overflow-hidden">
      <Breadcrumb items={generateBreadcrumbItems()} className={`text-sm ${className} truncate`} />
      {/* Hiệu ứng ánh sáng nhẹ */}
      <div className="absolute w-8 h-8 bg-primary opacity-10 rounded-full blur-xl -z-10 animate-pulse-slow"></div>
    </div>
  );
};

export default ViewBreadcrumb;
