import React, { useCallback, useState } from 'react';
import { Typo<PERSON>, Pagination, But<PERSON> } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ModernMenuItem } from '@/shared/components/common/ModernMenu';
import { useUserDataFineTuneList } from '../user-data-fine-tune/hooks/useUserDataFineTune';
import { DatasetGrid } from '../components';
import {
  DataFineTuneStatus,
  UserDataFineTuneQueryDto,
  UserDataFineTuneSortBy,
  UserDataFineTuneResponseDto,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';
import { useCreateFineTuneModel } from '../hooks/useCreateFineTuneModel';
import { CreateFineTuneModelDto } from '../user-data-fine-tune/types/fine-tune-model.types';
import CreateFineTuneModelSlideForm from '../components/CreateFineTuneModelModal';
import ProviderSelectionCards from '../components/ProviderSelectionModal';

// Interface cho backend response structure
interface BackendDatasetResponse {
  items: UserDataFineTuneResponseDto[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

/**
 * Data Fine-tune Page - Hiển thị danh sách dataset fine-tune
 */
const DataFineTunePage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [queryParams, setQueryParams] = useState<UserDataFineTuneQueryDto>({
    page: 1,
    limit: 12,
    search: '',

    sortBy: UserDataFineTuneSortBy.CREATED_AT,
  });

  const [showCreateModelForm, setShowCreateModelForm] = useState(false);

  const { data } = useUserDataFineTuneList(queryParams);
  const { createModel, isLoading: isCreatingModel } = useCreateFineTuneModel();
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const [showProviderCards, setShowProviderCards] = useState(false);

  const handleCreateNew = () => {
    // Toggle hiển thị cards
    setShowProviderCards(prev => !prev);
  };

  // Type assertion để match với backend response
  const backendData = data as unknown as BackendDatasetResponse;

  // Status labels
  const getStatusLabel = (status: DataFineTuneStatus) => {
    switch (status) {
      case DataFineTuneStatus.PENDING:
        return t('Đang chờ');
      case DataFineTuneStatus.PROCESSING:
        return t('Đang xử lý');
      case DataFineTuneStatus.COMPLETED:
        return t('Hoàn thành');
      case DataFineTuneStatus.FAILED:
        return t('Thất bại');
      case DataFineTuneStatus.CANCELLED:
        return t('Đã hủy');
      default:
        return status;
    }
  };

  // Handle search
  const handleSearch = (value: string) => {
    setQueryParams(prev => ({
      ...prev,
      search: value,
      page: 1,
    }));
  };

  // Xử lý thay đổi trang
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Xử lý thay đổi số mục trên trang
  const handleItemsPerPageChange = useCallback((newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset về trang 1 khi thay đổi số mục trên trang
  }, []);

  const handleSelectDataset = (id: string) => {
    // Navigate to dataset detail page (sẽ tạo sau)
    console.log('Navigate to dataset detail:', id);
  };
  const handleCloseCreateModelForm = () => {
    setShowCreateModelForm(false);
  };

  const handleSubmitCreateModel = async (data: CreateFineTuneModelDto) => {
    try {
      await createModel(data);
      setShowCreateModelForm(false);
      // Có thể thêm toast notification ở đây
      console.log('Fine-tune model created successfully');
      // Refresh data nếu cần
    } catch (error) {
      console.error('Error creating fine-tune model:', error);
      // Có thể thêm error notification ở đây
    }
  };

  // Handle create fine-tune model
  const handleCreateFineTuneModel = () => {
    setShowCreateModelForm(true);
  };

  // Menu items for filter
  const filterMenuItems: ModernMenuItem[] = [
    {
      id: 'all',
      label: t('Tất cả trạng thái'),
      onClick: () => {
        setQueryParams(prev => ({
          ...prev,

          page: 1,
        }));
      },
    },
    ...Object.values(DataFineTuneStatus).map(status => ({
      id: status,
      label: getStatusLabel(status),
      onClick: () => {
        setQueryParams(prev => ({
          ...prev,
          status,
          page: 1,
        }));
      },
    })),
  ];

  return (
    <div className="space-y-6">
      {/* Header with Create Fine-tuned Model button */}
      <div className="flex justify-between items-center">
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleCreateNew}
          items={filterMenuItems}
          showDateFilter={false}
          showColumnFilter={false}
        />

        <div className="flex items-center gap-2 ">
          <Button
            variant="primary"
            onClick={handleCreateFineTuneModel}
            className="ml-4"
            style={{
              backgroundColor: '#ff3333',
              color: 'white',
              padding: '8px 16px',
              border: 'none',
              borderRadius: '6px',
              minWidth: '200px',
              zIndex: 1000,
            }}
          >
            {t('Tạo Fine-tuned Model') || 'Tạo Fine-tuned Model'}
          </Button>
        </div>
      </div>
      {showProviderCards && (
        <ProviderSelectionCards
          onSelectOpenAI={() => {
            setShowProviderCards(false);
            navigate('/user-dataset/create-openai');
          }}
          onSelectGoogle={() => {
            setShowProviderCards(false);
            navigate('/user-dataset/create-google');
          }}
        />
      )}
      {/* Create Fine-tune Model SlideForm */}
      <CreateFineTuneModelSlideForm
        isVisible={showCreateModelForm}
        onClose={handleCloseCreateModelForm}
        onSubmit={handleSubmitCreateModel}
        isLoading={isCreatingModel}
      />

      {/* Dataset Grid */}
      {backendData?.items && backendData.items.length > 0 ? (
        <>
          <DatasetGrid
            datasets={backendData.items}
            onViewDataset={dataset => handleSelectDataset(dataset.id)}
            onEditDataset={dataset => {
              console.log('Navigate to edit dataset:', dataset.id);
            }}
            onDeleteDataset={dataset => {
              console.log('Delete dataset:', dataset.id);
            }}
          />

          {/* Pagination - Hiển thị khi có dữ liệu */}
          {backendData.meta && backendData.meta.totalItems > 0 && (
            <div className="flex justify-end mt-8">
              <Pagination
                variant="simple"
                currentPage={currentPage}
                itemsPerPage={itemsPerPage}
                totalPages={backendData.meta.totalPages}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                showFirstLastButtons={true}
                showItemsPerPageSelector={true}
                showPageInfo={false}
                itemsPerPageOptions={[10, 20, 50, 100]}
                size="md"
                borderless={true}
              />
            </div>
          )}
        </>
      ) : (
        <div className="text-center">
          <Typography variant="body1" color="muted" className="text-lg">
            {t('Không tìm thấy dataset nào')}
          </Typography>
        </div>
      )}
    </div>
  );
};

export default DataFineTunePage;
